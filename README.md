# Prompt Enhancer Desktop App

A desktop application built with Electron and React that enhances prompts using Google's Gemini AI API.

## Phase 1: Core Functionality ✅

This implementation includes:

- ✅ Basic Electron app setup
- ✅ Editor implementation with Monaco Editor
- ✅ Gemini API integration
- ✅ Simple enhancement mode with multiple styles

## Phase 2: Enhancement Modes ✅

- ✅ Structured Prompt Builder with customizable sections
- ✅ Template Generator with reusable templates
- ✅ Project Template Mode with structured outputs

## Phase 3: Advanced Features ✅

- ✅ Diff view for comparing original and enhanced prompts
- ✅ Quality checklist with dynamic validation
- ✅ History management with search and categorization
- ✅ Batch processing for multiple prompts

## Phase 4: Polish & Optimization ✅

- ✅ System tray integration with context menu
- ✅ Global keyboard shortcuts (Ctrl+Shift+P to show/hide, Ctrl+Shift+E for quick enhance)
- ✅ Enhanced clipboard integration with system notifications
- ✅ Performance optimizations with caching and debouncing
- ✅ Advanced error handling with retry mechanisms
- ✅ Toast notifications for better user feedback
- ✅ React Error Boundaries for graceful error recovery

## Features

### Enhancement Modes

- **Quick Enhancement**: Fast improvements for clarity and effectiveness
- **Structured Mode**: Organized prompts with clear sections
- **Template Mode**: Reusable template structures

### Enhancement Styles

- **Detailed**: Comprehensive, detailed instructions
- **Concise**: Brief and to-the-point
- **Creative**: Emphasizes creativity and exploration
- **Technical**: Technical accuracy and precision

### Core Features

- Monaco Editor for advanced text editing
- Real-time character and word count
- Enhanced clipboard functionality with system notifications
- API key management with secure storage
- Advanced error handling with retry mechanisms and user-friendly messages
- Performance optimizations with caching and debouncing
- System tray integration for quick access
- Global keyboard shortcuts for productivity
- Toast notifications for better user feedback
- React Error Boundaries for graceful error recovery

### System Integration

- **System Tray**: Minimize to tray with context menu for quick actions
- **Global Shortcuts**:
  - `Ctrl+Shift+P`: Show/hide application
  - `Ctrl+Shift+E`: Quick enhance from clipboard
  - `Ctrl+Enter`: Enhance current prompt
  - `Ctrl+C`: Copy enhanced prompt
  - `Ctrl+V`: Paste from clipboard
  - `Ctrl+D`: Toggle diff view
  - `Ctrl+B`: Toggle sidebar
- **Clipboard Integration**: Enhanced clipboard operations with notifications
- **System Notifications**: Native OS notifications for important events

### Performance Features

- **Caching**: Intelligent caching of enhancement results
- **Debouncing**: Prevents excessive API calls during typing
- **Error Recovery**: Automatic retry mechanisms with exponential backoff
- **Memory Management**: Efficient cache and history management
- **Background Processing**: Non-blocking operations for better UX

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Google Gemini API key

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd prompt-enhancer
```

2. Install dependencies:

```bash
npm install
```

3. Get your Gemini API key:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Sign in with your Google account
   - Create an API key
   - Copy the generated key

## Development

Run the development server:

```bash
npm run dev
```

This will start both the React development server and the Electron app.

## Building

Build the application:

```bash
npm run build
```

Package for distribution:

```bash
# For current platform
npm run package

# For specific platforms
npm run package:win
npm run package:mac
npm run package:linux
```

## Usage

1. Launch the application
2. Enter your Gemini API key when prompted
3. Select an enhancement mode and style
4. Enter your prompt in the input editor
5. Click "Enhance Prompt" to get an improved version
6. Copy the result or use it as input for further enhancement

## Project Structure

```
prompt-enhancer/
├── electron/           # Electron main process files
│   ├── main.ts        # Main Electron process
│   ├── preload.ts     # Preload script for IPC
│   └── utils.ts       # Utility functions
├── src/               # React application
│   ├── components/    # React components
│   ├── services/      # API services
│   ├── types/         # TypeScript definitions
│   └── App.tsx        # Main app component
├── dist/              # Built files
└── release/           # Packaged applications
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
