import {
  app,
  BrowserWindow,
  ipcMain,
  dialog,
  Tray,
  Menu,
  globalShortcut,
  clipboard,
  Notification,
  shell,
} from "electron";
import * as path from "path";
import * as fs from "fs";
import { isDev } from "./utils";
import * as dotenv from "dotenv";

dotenv.config();

class AppWindow {
  private mainWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  private isQuitting = false;

  constructor() {
    app.whenReady().then(() => {
      this.createMainWindow();
      this.createSystemTray();
      this.registerGlobalShortcuts();

      app.on("activate", () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on("before-quit", () => {
      this.isQuitting = true;
    });

    app.on("window-all-closed", () => {
      if (process.platform !== "darwin") {
        app.quit();
      }
    });

    app.on("will-quit", () => {
      // Unregister all shortcuts
      globalShortcut.unregisterAll();
    });

    this.setupIpcHandlers();
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, "preload.js"),
      },
      titleBarStyle: "default",
      show: false,
      icon: this.getAppIcon(),
    });

    // Load the React app
    if (isDev()) {
      this.mainWindow.loadURL("http://localhost:5173");
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, "../react/index.html"));
    }

    this.mainWindow.once("ready-to-show", () => {
      this.mainWindow?.show();
    });

    // Handle window close - minimize to tray instead of closing
    this.mainWindow.on("close", (event) => {
      if (!this.isQuitting && this.tray) {
        event.preventDefault();
        this.mainWindow?.hide();

        // Show notification on first minimize
        if (Notification.isSupported()) {
          new Notification({
            title: "Prompt Enhancer",
            body: "App minimized to system tray. Click the tray icon to restore.",
            silent: true,
          }).show();
        }
      }
    });

    this.mainWindow.on("closed", () => {
      this.mainWindow = null;
    });
  }

  private createSystemTray(): void {
    try {
      const iconPath = this.getTrayIcon();
      this.tray = new Tray(iconPath);
    } catch (error) {
      console.warn("Failed to create system tray:", error);
      // Skip tray creation if icon is missing
      return;
    }

    const contextMenu = Menu.buildFromTemplate([
      {
        label: "Show Prompt Enhancer",
        click: () => {
          this.showMainWindow();
        },
      },
      {
        label: "Quick Enhance from Clipboard",
        click: () => {
          this.quickEnhanceFromClipboard();
        },
      },
      { type: "separator" },
      {
        label: "Settings",
        click: () => {
          this.showMainWindow();
          // Send message to renderer to open settings
          this.mainWindow?.webContents.send("open-settings");
        },
      },
      { type: "separator" },
      {
        label: "Quit",
        click: () => {
          this.isQuitting = true;
          app.quit();
        },
      },
    ]);

    if (this.tray) {
      this.tray.setContextMenu(contextMenu);
      this.tray.setToolTip("Prompt Enhancer");

      // Double-click to show window
      this.tray.on("double-click", () => {
        this.showMainWindow();
      });
    }
  }

  private registerGlobalShortcuts(): void {
    // Global shortcut to show/hide the app
    globalShortcut.register("CommandOrControl+Shift+P", () => {
      if (this.mainWindow?.isVisible()) {
        this.mainWindow.hide();
      } else {
        this.showMainWindow();
      }
    });

    // Global shortcut for quick enhance from clipboard
    globalShortcut.register("CommandOrControl+Shift+E", () => {
      this.quickEnhanceFromClipboard();
    });
  }

  private showMainWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
    } else {
      this.createMainWindow();
    }
  }

  private async quickEnhanceFromClipboard(): Promise<void> {
    try {
      const clipboardText = clipboard.readText();
      if (clipboardText.trim()) {
        this.showMainWindow();
        // Send clipboard text to renderer for quick enhancement
        this.mainWindow?.webContents.send(
          "quick-enhance-clipboard",
          clipboardText
        );
      } else {
        if (Notification.isSupported()) {
          new Notification({
            title: "Prompt Enhancer",
            body: "Clipboard is empty or contains no text.",
          }).show();
        }
      }
    } catch (error) {
      console.error("Failed to read clipboard:", error);
    }
  }

  private getAppIcon(): string {
    const iconName = process.platform === "win32" ? "icon.ico" : "icon.png";
    return path.join(__dirname, "../assets", iconName);
  }

  private getTrayIcon(): string {
    let iconName: string;
    if (process.platform === "win32") {
      iconName = "tray-icon.ico";
    } else if (process.platform === "darwin") {
      iconName = "tray-iconTemplate.png"; // macOS template icon
    } else {
      iconName = "tray-icon.png";
    }
    return path.join(__dirname, "../assets", iconName);
  }

  private setupIpcHandlers(): void {
    const userDataPath = app.getPath("userData");
    const configPath = path.join(userDataPath, "app-config.json");

    const readConfig = () => {
      if (fs.existsSync(configPath)) {
        try {
          return JSON.parse(fs.readFileSync(configPath, "utf-8"));
        } catch (e) {
          console.error("Failed to parse config file:", e);
          return {};
        }
      }
      return {};
    };

    const writeConfig = (config: any) => {
      try {
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      } catch (e) {
        console.error("Failed to write config file:", e);
      }
    };

    // Handle API key storage/retrieval
    ipcMain.handle("store-api-key", async (_, apiKey: string) => {
      const config = readConfig();
      config.apiKey = apiKey;
      writeConfig(config);
      return { success: true };
    });

    ipcMain.handle("get-api-key", async () => {
      if (isDev()) {
        return { apiKey: process.env.GEMINI_API_KEY || null };
      } else {
        const config = readConfig();
        return { apiKey: config.apiKey || null };
      }
    });

    // Handle model selection storage/retrieval
    ipcMain.handle("store-selected-model", async (_, modelId: string) => {
      const config = readConfig();
      config.selectedModel = modelId;
      writeConfig(config);
      return { success: true };
    });

    ipcMain.handle("get-selected-model", async () => {
      const config = readConfig();
      return { modelId: config.selectedModel || null };
    });

    // Handle file operations
    ipcMain.handle("show-save-dialog", async () => {
      if (!this.mainWindow) return { canceled: true };

      const result = await dialog.showSaveDialog(this.mainWindow, {
        filters: [
          { name: "Text Files", extensions: ["txt"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      return result;
    });

    ipcMain.handle("is-dev", () => {
      return isDev();
    });

    ipcMain.handle("show-open-dialog", async () => {
      if (!this.mainWindow) return { canceled: true };

      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ["openFile"],
        filters: [
          { name: "Text Files", extensions: ["txt"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      return result;
    });

    // Clipboard operations
    ipcMain.handle("clipboard-write-text", async (_, text: string) => {
      try {
        clipboard.writeText(text);
        return { success: true };
      } catch (error) {
        console.error("Failed to write to clipboard:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    });

    ipcMain.handle("clipboard-read-text", async () => {
      try {
        const text = clipboard.readText();
        return { success: true, text };
      } catch (error) {
        console.error("Failed to read from clipboard:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    });

    // System notifications
    ipcMain.handle(
      "show-notification",
      async (_, options: { title: string; body: string; silent?: boolean }) => {
        try {
          if (Notification.isSupported()) {
            new Notification({
              title: options.title,
              body: options.body,
              silent: options.silent || false,
            }).show();
            return { success: true };
          } else {
            return { success: false, error: "Notifications not supported" };
          }
        } catch (error) {
          console.error("Failed to show notification:", error);
          return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          };
        }
      }
    );

    // Open external links
    ipcMain.handle("open-external", async (_, url: string) => {
      try {
        await shell.openExternal(url);
        return { success: true };
      } catch (error) {
        console.error("Failed to open external URL:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    });

    // Window management
    ipcMain.handle("minimize-to-tray", async () => {
      if (this.mainWindow && this.tray) {
        this.mainWindow.hide();
        return { success: true };
      }
      return { success: false, error: "Window or tray not available" };
    });

    ipcMain.handle("show-window", async () => {
      this.showMainWindow();
      return { success: true };
    });
  }
}

// Initialize the application
new AppWindow();
