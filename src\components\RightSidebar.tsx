import { FC, useMemo } from "react";
import { QualityChecklist } from "./QualityChecklist";
import { HistoryPanel } from "./HistoryPanel";
import { BatchProcessor } from "./BatchProcessor";
import {
  EnhancementMode,
  PromptHistory,
  BatchProcessingState,
  EnhancementStyle,
  LayoutState,
} from "../types";

interface RightSidebarProps {
  layout: LayoutState;
  currentMode: EnhancementMode;
  currentStyle: EnhancementStyle;
  originalPrompt: string;
  enhancedPrompt: string;
  history: PromptHistory[];
  batchState: BatchProcessingState;
  onLayoutChange: (layout: Partial<LayoutState>) => void;
  onSelectHistory: (item: PromptHistory) => void;
  onDeleteHistory: (id: string) => void;
  onClearHistory: () => void;
  onImportBatchFile: (file: File) => void;
  onStartBatchProcessing: () => void;
  onStopBatchProcessing: () => void;
  onExportBatchResults: () => void;
  onClearBatch: () => void;
  onRemoveBatchItem: (id: string) => void;
  className?: string;
}

export const RightSidebar: FC<RightSidebarProps> = ({
  layout,
  currentMode,
  currentStyle,
  originalPrompt,
  enhancedPrompt,
  history,
  batchState,
  onLayoutChange,
  onSelectHistory,
  onDeleteHistory,
  onClearHistory,
  onImportBatchFile,
  onStartBatchProcessing,
  onStopBatchProcessing,
  onExportBatchResults,
  onClearBatch,
  onRemoveBatchItem,
  className = "",
}) => {
  const tabs = [
    { id: "quality", label: "Quality", icon: "✓" },
    { id: "history", label: "History", icon: "⏱" },
    { id: "batch", label: "Batch", icon: "⚡" },
  ] as const;

  const handleTabChange = (tab: (typeof tabs)[number]["id"]) => {
    onLayoutChange({ rightSidebarTab: tab });
  };

  const toggleSidebar = () => {
    onLayoutChange({ showRightSidebar: !layout.showRightSidebar });
  };

  if (!layout.showRightSidebar) {
    // Get quick stats for the minimized view
    const qualityStats = useMemo(() => {
      if (!originalPrompt && !enhancedPrompt) return null;

      // Simple quality indicators
      const hasContent = originalPrompt.length > 20;
      const hasEnhancement = enhancedPrompt.length > 0;
      const isImproved = enhancedPrompt.length > originalPrompt.length * 1.2;

      return { hasContent, hasEnhancement, isImproved };
    }, [originalPrompt, enhancedPrompt]);

    const historyCount = history.length;
    const batchCount = batchState.items.length;
    const batchProgress =
      batchState.totalItems > 0
        ? Math.round(
            ((batchState.completedItems + batchState.failedItems) /
              batchState.totalItems) *
              100
          )
        : 0;

    return (
      <div
        className={`flex flex-col bg-gradient-to-b from-white to-gray-50 border-l border-gray-200 shadow-lg ${className}`}
        style={{ width: "52px" }}
      >
        {/* Toggle Button */}
        <div className="p-2 border-b border-gray-200 bg-white">
          <button
            onClick={toggleSidebar}
            className="minimized-sidebar-button w-9 h-9 bg-gradient-to-r from-primary-50 to-primary-100 border border-primary-200 rounded-lg hover:from-primary-100 hover:to-primary-200 hover:border-primary-300 hover:shadow-md flex items-center justify-center group transition-all duration-200"
            title="Show Tools Sidebar"
          >
            <svg
              className="w-4 h-4 text-primary-600 group-hover:text-primary-700 transition-colors duration-200"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            <div className="minimized-tooltip">Show Tools</div>
          </button>
        </div>

        {/* Quick Access Icons */}
        <div className="flex-1 flex flex-col space-y-2 p-2">
          {/* Quality Indicator */}
          <button
            onClick={() => {
              toggleSidebar();
              onLayoutChange({ rightSidebarTab: "quality" });
            }}
            className="minimized-sidebar-button w-9 h-9 rounded-lg hover:bg-green-50 hover:border hover:border-green-200 hover:shadow-md flex items-center justify-center group relative transition-all duration-200"
            title="Quality Checklist"
          >
            <div className="relative">
              <span
                className={`text-lg transition-colors ${
                  qualityStats?.hasEnhancement && qualityStats?.isImproved
                    ? "text-green-600"
                    : qualityStats?.hasContent
                    ? "text-yellow-600"
                    : "text-gray-500"
                }`}
              >
                ✓
              </span>
              {qualityStats && (
                <div
                  className={`notification-badge absolute -top-1 -right-1 w-3 h-3 rounded-full border border-white ${
                    qualityStats.hasEnhancement && qualityStats.isImproved
                      ? "bg-green-500"
                      : qualityStats.hasContent
                      ? "bg-yellow-500"
                      : "bg-gray-400"
                  }`}
                />
              )}
            </div>
            <div className="minimized-tooltip">
              Quality Check
              {qualityStats && (
                <div className="text-xs opacity-75">
                  {qualityStats.hasEnhancement && qualityStats.isImproved
                    ? "Excellent"
                    : qualityStats.hasContent
                    ? "Good"
                    : "Needs work"}
                </div>
              )}
            </div>
          </button>

          {/* History Indicator */}
          <button
            onClick={() => {
              toggleSidebar();
              onLayoutChange({ rightSidebarTab: "history" });
            }}
            className="minimized-sidebar-button w-9 h-9 rounded-lg hover:bg-blue-50 hover:border hover:border-blue-200 hover:shadow-md flex items-center justify-center group relative transition-all duration-200"
            title={`History (${historyCount} items)`}
          >
            <div className="relative">
              <span
                className={`text-lg transition-colors ${
                  historyCount > 0 ? "text-blue-600" : "text-gray-500"
                }`}
              >
                ⏱
              </span>
              {historyCount > 0 && (
                <div className="notification-badge absolute -top-1 -right-1 w-4 h-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-sm">
                  {historyCount > 9 ? "9+" : historyCount}
                </div>
              )}
            </div>
            <div className="minimized-tooltip">
              History
              <div className="text-xs opacity-75">
                {historyCount} {historyCount === 1 ? "item" : "items"}
              </div>
            </div>
          </button>

          {/* Batch Indicator */}
          <button
            onClick={() => {
              toggleSidebar();
              onLayoutChange({ rightSidebarTab: "batch" });
            }}
            className="minimized-sidebar-button w-9 h-9 rounded-lg hover:bg-purple-50 hover:border hover:border-purple-200 hover:shadow-md flex items-center justify-center group relative transition-all duration-200"
            title={`Batch Processing (${batchCount} items)`}
          >
            <div className="relative">
              <span
                className={`text-lg transition-colors ${
                  batchState.isProcessing
                    ? "text-orange-600"
                    : batchCount > 0
                    ? "text-purple-600"
                    : "text-gray-500"
                }`}
              >
                ⚡
              </span>
              {batchState.isProcessing && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-500 rounded-full animate-pulse" />
              )}
              {batchCount > 0 && !batchState.isProcessing && (
                <div className="notification-badge absolute -top-1 -right-1 w-4 h-4 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-sm">
                  {batchCount > 9 ? "9+" : batchCount}
                </div>
              )}
            </div>
            <div className="minimized-tooltip">
              Batch Processing
              <div className="text-xs opacity-75">
                {batchState.isProcessing
                  ? `Processing... ${batchProgress}%`
                  : `${batchCount} ${batchCount === 1 ? "item" : "items"}`}
              </div>
            </div>
          </button>
        </div>

        {/* Progress Indicators */}
        <div className="p-3 border-t border-gray-200 space-y-4 bg-white">
          {/* Batch Progress */}
          {batchState.isProcessing && (
            <div className="w-full">
              <div className="w-3 h-14 bg-gray-200 rounded-full mx-auto relative overflow-hidden shadow-inner">
                <div
                  className="progress-bar-fill absolute bottom-0 left-0 right-0 bg-gradient-to-t from-orange-500 via-orange-400 to-yellow-400 rounded-full transition-all duration-300"
                  style={{ height: `${batchProgress}%` }}
                />
                {/* Animated glow effect */}
                <div
                  className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-orange-300 to-transparent rounded-full opacity-50 animate-pulse"
                  style={{ height: `${Math.min(batchProgress + 10, 100)}%` }}
                />
              </div>
              <div className="text-xs text-center text-orange-600 font-medium mt-2">
                {batchProgress}%
              </div>
            </div>
          )}

          {/* Quick Stats */}
          <div className="space-y-1">
            {qualityStats && (
              <div className="text-center">
                <div
                  className={`w-1 h-1 rounded-full mx-auto ${
                    qualityStats.hasEnhancement && qualityStats.isImproved
                      ? "bg-green-500"
                      : qualityStats.hasContent
                      ? "bg-yellow-500"
                      : "bg-gray-400"
                  }`}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`flex flex-col bg-gradient-to-b from-gray-50 to-white ${className}`}
    >
      {/* Header */}
      <button
        onClick={toggleSidebar}
        className="tools-header-button flex items-center justify-between py-3 px-4 bg-white border-b border-gray-200 hover:bg-gray-50 transition-all duration-200 w-full text-left group shadow-sm"
        title="Click to hide Tools sidebar"
      >
        <div className="flex items-center space-x-3">
          <h2 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
            Tools
          </h2>
          <div className="w-2 h-2 bg-primary-500 rounded-full opacity-60 group-hover:opacity-100 group-hover:scale-110 transition-all duration-200" />
        </div>
        <svg
          className="w-5 h-5 text-gray-400 group-hover:text-primary-500 group-hover:scale-105 transition-all duration-200"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 bg-white shadow-sm">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-all duration-200 relative ${
              layout.rightSidebarTab === tab.id
                ? "border-primary-500 text-primary-600 bg-primary-50"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <span className="text-base">{tab.icon}</span>
              <span className="font-medium">{tab.label}</span>
            </div>
            {/* Active tab indicator */}
            {layout.rightSidebarTab === tab.id && (
              <div className="absolute inset-x-0 bottom-0 h-0.5 bg-primary-500 rounded-t-full" />
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 p-5 overflow-hidden bg-white">
        {layout.rightSidebarTab === "quality" && (
          <QualityChecklist
            mode={currentMode}
            originalPrompt={originalPrompt}
            enhancedPrompt={enhancedPrompt}
            className="h-full"
          />
        )}

        {layout.rightSidebarTab === "history" && (
          <HistoryPanel
            history={history}
            onSelectHistory={onSelectHistory}
            onDeleteHistory={onDeleteHistory}
            onClearHistory={onClearHistory}
            className="h-full"
          />
        )}

        {layout.rightSidebarTab === "batch" && (
          <BatchProcessor
            batchState={batchState}
            currentStyle={currentStyle}
            onImportFile={onImportBatchFile}
            onStartProcessing={onStartBatchProcessing}
            onStopProcessing={onStopBatchProcessing}
            onExportResults={onExportBatchResults}
            onClearBatch={onClearBatch}
            onRemoveItem={onRemoveBatchItem}
            className="h-full"
          />
        )}
      </div>

      {/* Footer with additional controls */}
      <div className="px-5 py-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 shadow-inner">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            {layout.rightSidebarTab === "quality" && enhancedPrompt && (
              <button
                onClick={() =>
                  onLayoutChange({ showDiffView: !layout.showDiffView })
                }
                className={`px-3 py-2 rounded-md text-xs font-medium transition-all duration-200 ${
                  layout.showDiffView
                    ? "bg-primary-100 text-primary-700 border border-primary-200 shadow-sm"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-700 border border-gray-200 hover:border-gray-300"
                }`}
              >
                {layout.showDiffView ? "Hide Diff" : "Show Diff"}
              </button>
            )}

            {layout.rightSidebarTab === "history" && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-xs font-medium text-gray-600">
                  {history.length} items
                </span>
              </div>
            )}

            {layout.rightSidebarTab === "batch" && (
              <div className="flex items-center space-x-2">
                <div
                  className={`w-2 h-2 rounded-full ${
                    batchState.isProcessing
                      ? "bg-orange-500 animate-pulse"
                      : "bg-purple-500"
                  }`}
                ></div>
                <span className="text-xs font-medium text-gray-600">
                  {batchState.items.length} items
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
