import { useState, useEffect } from "react";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { ToastContainer } from "./components/ToastContainer";
import { Editor } from "./components/Editor";
import { StructuredPromptEditor } from "./components/StructuredPromptEditor";
import { TemplateEditor } from "./components/TemplateEditor";
import { ApiKeyModal } from "./components/ApiKeyModal";
import { ModelSelector } from "./components/ModelSelector";
import { RightSidebar } from "./components/RightSidebar";
import { DiffViewer } from "./components/DiffViewer";
import { geminiService } from "./services/geminiService";
import { historyService } from "./services/historyService";
import { batchService } from "./services/batchService";
import { performanceService } from "./services/performanceService";
import { errorHandlingService } from "./services/errorHandlingService";
import { notificationService } from "./services/notificationService";
import {
  useKeyboardShortcuts,
  getDefaultShortcuts,
} from "./hooks/useKeyboardShortcuts";
import {
  AppState,
  EnhancementMode,
  EnhancementStyle,
  StructuredPromptSections,
  PromptTemplate,
  SectionConfig,
  PromptHistory,
  LayoutState,
  BatchItem,
} from "./types";

function App() {
  const [state, setState] = useState<AppState>({
    originalPrompt: "",
    enhancedPrompt: "",
    isLoading: false,
    error: null,
    apiKey: null,
    currentMode: "quick",
    currentStyle: "detailed",
    structuredSections: {
      role: "",
      context: "",
      instructions: "",
      goal: "",
      constraints: "",
      examples: "",
      outputFormat: "",
    },
    selectedTemplate: null,
    customTemplates: [],
    customSections: [], // Will be populated with default sections
    layout: {
      showRightSidebar: true,
      rightSidebarTab: "quality",
      showDiffView: false,
    },
    batchProcessing: {
      items: [],
      isProcessing: false,
      currentIndex: 0,
      totalItems: 0,
      completedItems: 0,
      failedItems: 0,
    },
    history: [],
  });

  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  useEffect(() => {
    // Check if API key and selected model are stored
    if (window.electronAPI) {
      Promise.all([
        window.electronAPI.getApiKey(),
        window.electronAPI.getSelectedModel(),
      ]).then(([{ apiKey }, { modelId }]) => {
        if (apiKey) {
          setState((prev) => ({ ...prev, apiKey }));
          geminiService.initialize({
            apiKey,
            model: modelId || undefined,
          });
          setSelectedModel(modelId);
        } else {
          // Only show modal in production if API key is missing
          setShowApiKeyModal(true);
        }
      });

      // Set up system integration event listeners
      window.electronAPI.onQuickEnhanceClipboard((text) => {
        setState((prev) => ({ ...prev, originalPrompt: text }));
        notificationService.info("Clipboard text loaded for enhancement");
        // Auto-enhance after a short delay
        setTimeout(() => handleEnhancePrompt(), 1000);
      });

      window.electronAPI.onOpenSettings(() => {
        setShowApiKeyModal(true);
      });
    }

    // Load history on startup
    loadHistory();

    // Cleanup on unmount
    return () => {
      performanceService.cleanup();
    };
  }, []);

  const loadHistory = async () => {
    try {
      const history = await historyService.getHistory();
      setState((prev) => ({ ...prev, history }));
    } catch (error) {
      console.error("Failed to load history:", error);
    }
  };

  const handleApiKeySubmit = async (apiKey: string) => {
    try {
      geminiService.initialize({
        apiKey,
        model: selectedModel || undefined,
      });

      if (window.electronAPI) {
        await window.electronAPI.storeApiKey(apiKey);
      }

      setState((prev) => ({ ...prev, apiKey }));
      setShowApiKeyModal(false);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error:
          "Failed to initialize Gemini service. Please check your API key.",
      }));
    }
  };

  const handleModelSelect = async (modelId: string) => {
    try {
      // Update the service to use the new model
      geminiService.updateModel(modelId);

      // Store the selected model
      if (window.electronAPI) {
        await window.electronAPI.storeSelectedModel(modelId);
      }

      setSelectedModel(modelId);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: "Failed to update model selection.",
      }));
    }
  };

  const handleEnhancePrompt = async () => {
    // Get the prompt content based on current mode
    let promptToEnhance = "";

    if (state.currentMode === "structured") {
      // Generate combined prompt from structured sections using the same logic as the component
      const allSections =
        state.customSections.length > 0 ? state.customSections : [];
      const parts: string[] = [];

      allSections.forEach((section) => {
        const content = state.structuredSections[section.key];
        if (content && content.trim()) {
          // Remove "(Optional)" text from the label for the combined output
          const cleanLabel = section.label.replace(/\s*\(Optional\)/i, "");
          parts.push(`**${cleanLabel.toUpperCase()}:**\n${content}`);
        }
      });

      promptToEnhance = parts.join("\n\n");
    } else {
      promptToEnhance = state.originalPrompt;
    }

    if (!promptToEnhance.trim()) {
      setState((prev) => ({
        ...prev,
        error: "Please enter a prompt to enhance.",
      }));
      return;
    }

    if (!geminiService.isInitialized()) {
      setShowApiKeyModal(true);
      return;
    }

    // Check cache first
    const cacheKey = performanceService.generateEnhancementCacheKey(
      promptToEnhance,
      state.currentMode,
      state.currentStyle
    );
    const cachedResult = performanceService.getCache(cacheKey);

    if (cachedResult) {
      setState((prev) => ({
        ...prev,
        enhancedPrompt: cachedResult as string,
      }));
      notificationService.success("Enhancement loaded from cache");
      return;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    const progressId = notificationService.showProgress(
      "Enhancement",
      "Processing your prompt",
      0
    );

    try {
      // Use error handling service with retry
      const response = await errorHandlingService.withRetry(
        async () => {
          notificationService.updateProgress(
            progressId,
            50,
            "Calling AI service"
          );
          return await geminiService.enhancePrompt({
            originalPrompt: promptToEnhance,
            mode: state.currentMode,
            style: state.currentStyle,
          });
        },
        { maxRetries: 2 }
      );

      // Cache the result
      performanceService.setCache(cacheKey, response.enhancedPrompt);

      setState((prev) => ({
        ...prev,
        enhancedPrompt: response.enhancedPrompt,
        isLoading: false,
      }));

      notificationService.completeProgress(
        progressId,
        "Prompt enhanced successfully!"
      );

      // Save to history
      try {
        const historyItem = await historyService.saveToHistory(
          promptToEnhance,
          response.enhancedPrompt,
          state.currentMode,
          state.currentStyle
        );
        setState((prev) => ({
          ...prev,
          history: [historyItem, ...prev.history],
        }));
      } catch (error) {
        console.error("Failed to save to history:", error);
      }
    } catch (error) {
      const errorMessage = errorHandlingService.getUserFriendlyMessage(
        error instanceof Error ? error : new Error(String(error))
      );

      setState((prev) => ({
        ...prev,
        error: errorMessage,
        isLoading: false,
      }));

      notificationService.failProgress(progressId, errorMessage);
    }
  };

  const handleModeChange = (mode: EnhancementMode) => {
    setState((prev) => ({ ...prev, currentMode: mode }));
  };

  const handleStyleChange = (style: EnhancementStyle) => {
    setState((prev) => ({ ...prev, currentStyle: style }));
  };

  const handlePromptChange = (prompt: string) => {
    setState((prev) => ({ ...prev, originalPrompt: prompt }));
  };

  const handleClearError = () => {
    setState((prev) => ({ ...prev, error: null }));
  };

  const handleStructuredSectionsChange = (
    sections: StructuredPromptSections
  ) => {
    setState((prev) => ({ ...prev, structuredSections: sections }));
  };

  const handleTemplateSelect = (template: PromptTemplate | null) => {
    setState((prev) => ({ ...prev, selectedTemplate: template }));
  };

  const handleCustomSectionsChange = (sections: SectionConfig[]) => {
    setState((prev) => ({ ...prev, customSections: sections }));
  };

  // Layout handlers
  const handleLayoutChange = (layoutChanges: Partial<LayoutState>) => {
    setState((prev) => ({
      ...prev,
      layout: { ...prev.layout, ...layoutChanges },
    }));
  };

  // History handlers
  const handleSelectHistory = (item: PromptHistory) => {
    setState((prev) => ({
      ...prev,
      originalPrompt: item.originalPrompt,
      enhancedPrompt: item.enhancedPrompt,
      currentMode: item.mode,
      currentStyle: item.style,
    }));
  };

  const handleDeleteHistory = async (id: string) => {
    try {
      await historyService.deleteHistoryItem(id);
      setState((prev) => ({
        ...prev,
        history: prev.history.filter((item) => item.id !== id),
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: "Failed to delete history item",
      }));
    }
  };

  const handleClearHistory = async () => {
    try {
      await historyService.clearHistory();
      setState((prev) => ({ ...prev, history: [] }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: "Failed to clear history",
      }));
    }
  };

  // Batch processing handlers
  const handleImportBatchFile = async (file: File) => {
    try {
      const prompts = await batchService.parseTextFile(file);
      const batchItems = batchService.createBatchItems(prompts);

      const validation = batchService.validateBatchSize(batchItems);
      if (!validation.isValid) {
        setState((prev) => ({
          ...prev,
          error: validation.message || "Invalid batch",
        }));
        return;
      }

      setState((prev) => ({
        ...prev,
        batchProcessing: {
          ...prev.batchProcessing,
          items: batchItems,
          totalItems: batchItems.length,
          completedItems: 0,
          failedItems: 0,
        },
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: "Failed to import batch file. Please check the file format.",
      }));
    }
  };

  const handleStartBatchProcessing = async () => {
    const { items } = state.batchProcessing;
    if (items.length === 0) return;

    setState((prev) => ({
      ...prev,
      batchProcessing: { ...prev.batchProcessing, isProcessing: true },
    }));

    await batchService.processBatch(
      items,
      state.currentStyle,
      (batchState) => {
        setState((prev) => ({ ...prev, batchProcessing: batchState }));
      },
      (results) => {
        setState((prev) => ({
          ...prev,
          batchProcessing: { ...prev.batchProcessing, items: results },
        }));
      }
    );
  };

  const handleStopBatchProcessing = () => {
    batchService.stopProcessing();
  };

  const handleExportBatchResults = async () => {
    try {
      const content = await batchService.exportResults(
        state.batchProcessing.items,
        "json"
      );
      const filename = `batch-results-${
        new Date().toISOString().split("T")[0]
      }.json`;
      await batchService.downloadFile(content, filename, "application/json");
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: "Failed to export batch results",
      }));
    }
  };

  const handleClearBatch = () => {
    setState((prev) => ({
      ...prev,
      batchProcessing: {
        items: [],
        isProcessing: false,
        currentIndex: 0,
        totalItems: 0,
        completedItems: 0,
        failedItems: 0,
      },
    }));
  };

  const handleRemoveBatchItem = (id: string) => {
    setState((prev) => ({
      ...prev,
      batchProcessing: {
        ...prev.batchProcessing,
        items: prev.batchProcessing.items.filter((item) => item.id !== id),
        totalItems: prev.batchProcessing.totalItems - 1,
      },
    }));
  };

  // New Phase 4 handlers
  const handleCopyEnhanced = async () => {
    if (!state.enhancedPrompt) {
      notificationService.warning("No enhanced prompt to copy");
      return;
    }

    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.clipboardWriteText(
          state.enhancedPrompt
        );
        if (result.success) {
          notificationService.clipboardSuccess(state.enhancedPrompt);
        } else {
          throw new Error(result.error);
        }
      } else {
        await navigator.clipboard.writeText(state.enhancedPrompt);
        notificationService.clipboardSuccess(state.enhancedPrompt);
      }
    } catch (error) {
      notificationService.error("Failed to copy to clipboard");
    }
  };

  const handlePasteFromClipboard = async () => {
    try {
      let text = "";
      if (window.electronAPI) {
        const result = await window.electronAPI.clipboardReadText();
        if (result.success && result.text) {
          text = result.text;
        } else {
          throw new Error(result.error);
        }
      } else {
        text = await navigator.clipboard.readText();
      }

      if (text.trim()) {
        setState((prev) => ({ ...prev, originalPrompt: text }));
        notificationService.success("Text pasted from clipboard");
      } else {
        notificationService.warning("Clipboard is empty");
      }
    } catch (error) {
      notificationService.error("Failed to read from clipboard");
    }
  };

  const handleClearPrompt = () => {
    setState((prev) => ({
      ...prev,
      originalPrompt: "",
      enhancedPrompt: "",
      error: null,
    }));
    notificationService.info("Prompt cleared");
  };

  const handleToggleDiffView = () => {
    if (!state.enhancedPrompt) {
      notificationService.warning("No enhanced prompt to compare");
      return;
    }
    setState((prev) => ({
      ...prev,
      layout: {
        ...prev.layout,
        showDiffView: !prev.layout.showDiffView,
      },
    }));
  };

  const handleToggleSidebar = () => {
    setState((prev) => ({
      ...prev,
      layout: {
        ...prev.layout,
        showRightSidebar: !prev.layout.showRightSidebar,
      },
    }));
  };

  const handleNewPrompt = () => {
    setState((prev) => ({
      ...prev,
      originalPrompt: "",
      enhancedPrompt: "",
      error: null,
      structuredSections: {
        role: "",
        context: "",
        instructions: "",
        goal: "",
        constraints: "",
        examples: "",
        outputFormat: "",
      },
    }));
    notificationService.info("New prompt started");
  };

  const handleSavePrompt = async () => {
    if (!state.enhancedPrompt) {
      notificationService.warning("No enhanced prompt to save");
      return;
    }

    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.showSaveDialog();
        if (!result.canceled && result.filePath) {
          // In a real implementation, you'd save the file here
          notificationService.success("Prompt saved successfully");
        }
      }
    } catch (error) {
      notificationService.error("Failed to save prompt");
    }
  };

  const handleMinimizeToTray = async () => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.minimizeToTray();
        if (!result.success) {
          throw new Error(result.error);
        }
      }
    } catch (error) {
      notificationService.error("Failed to minimize to tray");
    }
  };

  // Keyboard shortcuts setup
  const shortcuts = getDefaultShortcuts({
    enhance: () => handleEnhancePrompt(),
    copy: () => handleCopyEnhanced(),
    paste: () => handlePasteFromClipboard(),
    clear: () => handleClearPrompt(),
    toggleDiff: () => handleToggleDiffView(),
    toggleSidebar: () => handleToggleSidebar(),
    openSettings: () => setShowApiKeyModal(true),
    newPrompt: () => handleNewPrompt(),
    save: () => handleSavePrompt(),
    undo: () => {}, // TODO: Implement undo/redo
    redo: () => {}, // TODO: Implement undo/redo
    selectAll: () => {}, // Handled by Monaco editor
    find: () => {}, // Handled by Monaco editor
    minimizeToTray: () => handleMinimizeToTray(),
  });

  useKeyboardShortcuts(shortcuts.flatMap((group) => group.shortcuts));

  return (
    <ErrorBoundary>
      <div className="min-h-screen h-screen bg-gray-50 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="sticky top-0 z-50 bg-white shadow-sm border-b border-gray-200 px-6 py-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">
              Prompt Enhancer
            </h1>
            <div className="flex items-center space-x-4">
              {/* Mode Selection */}
              <div className="relative">
                <select
                  value={state.currentMode}
                  onChange={(e) =>
                    handleModeChange(e.target.value as EnhancementMode)
                  }
                  className="px-3 py-2 pr-10 bg-white text-gray-900 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none"
                >
                  <option value="quick">Quick Enhancement</option>
                  <option value="structured">Structured Prompt Builder</option>
                  <option value="template">Template Generator</option>
                  <option value="batch">Batch Processing</option>
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </div>

              {/* Style Selection */}
              <div className="relative">
                <select
                  value={state.currentStyle}
                  onChange={(e) =>
                    handleStyleChange(e.target.value as EnhancementStyle)
                  }
                  className="px-3 py-2 pr-10 bg-white text-gray-900 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none"
                >
                  <option value="detailed">Detailed</option>
                  <option value="concise">Concise</option>
                  <option value="creative">Creative</option>
                  <option value="technical">Technical</option>
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </div>

              {/* API Key Button */}
              <button
                onClick={() => setShowApiKeyModal(true)}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                {state.apiKey ? "Update API Key" : "Set API Key"}
              </button>
            </div>
          </div>
        </header>

        {/* Error Banner */}
        {state.error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4 rounded flex-shrink-0">
            <div className="flex items-center justify-between">
              <p className="text-red-700">{state.error}</p>
              <button
                onClick={handleClearError}
                className="text-red-400 hover:text-red-600"
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Model Selection */}
        {state.apiKey && (
          <div className="px-6 mt-4 flex-shrink-0">
            <ModelSelector
              selectedModel={selectedModel}
              onModelSelect={handleModelSelect}
              disabled={state.isLoading}
            />
          </div>
        )}

        {/* Main Content */}
        <main className="flex-1 flex overflow-hidden border-t border-gray-200 min-h-0">
          <div className="flex-1 p-6 overflow-auto min-h-0">
            {state.layout.showDiffView && state.enhancedPrompt ? (
              <DiffViewer
                originalText={state.originalPrompt}
                enhancedText={state.enhancedPrompt}
                className="h-full"
              />
            ) : state.currentMode === "structured" ? (
              <div className="h-full flex space-x-6">
                <div className="flex-1">
                  <StructuredPromptEditor
                    sections={state.structuredSections}
                    onSectionsChange={handleStructuredSectionsChange}
                    customSections={state.customSections}
                    onCustomSectionsChange={handleCustomSectionsChange}
                    isLoading={state.isLoading}
                    onEnhance={handleEnhancePrompt}
                  />
                </div>
                {state.enhancedPrompt && !state.layout.showRightSidebar && (
                  <div className="w-1/2">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                      <div className="px-4 py-3 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900">
                          Enhanced Structured Prompt
                        </h2>
                      </div>
                      <div className="h-full p-4">
                        <div className="h-full overflow-auto">
                          <pre className="whitespace-pre-wrap text-sm text-gray-800">
                            {state.enhancedPrompt}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : state.currentMode === "template" ? (
              <div className="h-full flex space-x-6">
                <div className="flex-1">
                  <TemplateEditor
                    originalPrompt={state.originalPrompt}
                    selectedTemplate={state.selectedTemplate}
                    onPromptChange={handlePromptChange}
                    onTemplateSelect={handleTemplateSelect}
                    isLoading={state.isLoading}
                    onEnhance={handleEnhancePrompt}
                  />
                </div>
                {state.enhancedPrompt && !state.layout.showRightSidebar && (
                  <div className="w-1/2">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                      <div className="px-4 py-3 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900">
                          Generated Template
                        </h2>
                      </div>
                      <div className="h-full p-4">
                        <div className="h-full overflow-auto">
                          <pre className="whitespace-pre-wrap text-sm text-gray-800">
                            {state.enhancedPrompt}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : state.currentMode === "batch" ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                    Batch Processing Mode
                  </h2>
                  <p className="text-gray-600 mb-6">
                    Use the Batch tab in the right sidebar to import and process
                    multiple prompts at once.
                  </p>
                  <button
                    onClick={() =>
                      handleLayoutChange({ rightSidebarTab: "batch" })
                    }
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    Open Batch Processor
                  </button>
                </div>
              </div>
            ) : (
              <Editor
                originalPrompt={state.originalPrompt}
                enhancedPrompt={state.enhancedPrompt}
                isLoading={state.isLoading}
                onPromptChange={handlePromptChange}
                onEnhance={handleEnhancePrompt}
              />
            )}
          </div>

          {/* Right Sidebar */}
          <RightSidebar
            layout={state.layout}
            currentMode={state.currentMode}
            currentStyle={state.currentStyle}
            originalPrompt={state.originalPrompt}
            enhancedPrompt={state.enhancedPrompt}
            history={state.history}
            batchState={state.batchProcessing}
            onLayoutChange={handleLayoutChange}
            onSelectHistory={handleSelectHistory}
            onDeleteHistory={handleDeleteHistory}
            onClearHistory={handleClearHistory}
            onImportBatchFile={handleImportBatchFile}
            onStartBatchProcessing={handleStartBatchProcessing}
            onStopBatchProcessing={handleStopBatchProcessing}
            onExportBatchResults={handleExportBatchResults}
            onClearBatch={handleClearBatch}
            onRemoveBatchItem={handleRemoveBatchItem}
            className="w-80 pt-6 pb-6 overflow-auto min-h-0"
          />
        </main>

        {/* API Key Modal */}
        {showApiKeyModal && (
          <ApiKeyModal
            onSubmit={handleApiKeySubmit}
            onClose={() => setShowApiKeyModal(false)}
          />
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer />
    </ErrorBoundary>
  );
}

export default App;
